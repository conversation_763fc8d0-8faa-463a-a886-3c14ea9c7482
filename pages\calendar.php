<?php
require_once '../includes/session.php';
require_login();

// Get current month and year
$month = isset($_GET['month']) ? $_GET['month'] : date('m');
$year = isset($_GET['year']) ? $_GET['year'] : date('Y');

// Get tasks for the current user for the given month and year
$user_id = $_SESSION['user_id'];
$tasks = [];
$sql = "SELECT id, title, deadline FROM tasks WHERE user_id = ? AND MONTH(deadline) = ? AND YEAR(deadline) = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("iss", $user_id, $month, $year);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $tasks[date('j', strtotime($row['deadline']))][] = $row['title'];
}
$stmt->close();

// Calendar logic
$first_day_of_month = mktime(0, 0, 0, $month, 1, $year);
$days_in_month = date('t', $first_day_of_month);
$day_of_week = date('w', $first_day_of_month);
$calendar_title = date('F Y', $first_day_of_month);

$prev_month = date('m', strtotime('-1 month', $first_day_of_month));
$prev_year = date('Y', strtotime('-1 month', $first_day_of_month));
$next_month = date('m', strtotime('+1 month', $first_day_of_month));
$next_year = date('Y', strtotime('+1 month', $first_day_of_month));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar - Time Management System</title>
    <link href="../src/output.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white font-sans">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-800/50 backdrop-blur-lg border-r border-gray-700 p-6 flex-shrink-0">
            <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 mb-12">TaskFlow</h1>
            <nav class="space-y-2">
                <a href="dashboard.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                    Dashboard
                </a>
                <a href="add_task.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    Add Task
                </a>
                <a href="calendar.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 bg-purple-600/30 text-white border border-purple-500">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                    Calendar
                </a>
            </nav>
            <div class="absolute bottom-6 left-6">
                <a href="../logout.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50 text-gray-400 hover:text-white">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 sm:p-10">
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-4xl font-bold"><?php echo $calendar_title; ?></h2>
                <div class="flex space-x-2">
                    <a href="?month=<?php echo $prev_month; ?>&year=<?php echo $prev_year; ?>" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">Prev</a>
                    <a href="?month=<?php echo $next_month; ?>&year=<?php echo $next_year; ?>" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition">Next</a>
                </div>
            </div>
            <div class="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl shadow-lg p-6">
                <div class="grid grid-cols-7 gap-1 text-center font-bold text-gray-400">
                    <div class="py-2">Sun</div>
                    <div class="py-2">Mon</div>
                    <div class="py-2">Tue</div>
                    <div class="py-2">Wed</div>
                    <div class="py-2">Thu</div>
                    <div class="py-2">Fri</div>
                    <div class="py-2">Sat</div>
                </div>
                <div class="grid grid-cols-7 gap-1">
                    <?php
                    // Print empty cells
                    for ($i = 0; $i < $day_of_week; $i++) {
                        echo "<div class='border border-gray-800 rounded-lg p-2 h-32'></div>";
                    }

                    // Print days
                    for ($day = 1; $day <= $days_in_month; $day++) {
                        $is_today = date('Y-m-d') == date('Y-m-d', mktime(0, 0, 0, $month, $day, $year));
                        $day_classes = "border border-gray-700 rounded-lg p-2 h-32 flex flex-col transition hover:bg-gray-700/50";
                        if ($is_today) {
                            $day_classes .= " bg-purple-600/20 border-purple-500";
                        }
                        echo "<div class='$day_classes'>";
                        echo "<div class='font-bold " . ($is_today ? 'text-purple-300' : '') . "'>$day</div>";
                        echo "<div class='mt-1 space-y-1 overflow-y-auto'>";
                        if (isset($tasks[$day])) {
                            foreach ($tasks[$day] as $task_title) {
                                echo "<div class='text-xs bg-pink-600/50 border border-pink-500 text-white rounded-md px-2 py-1 truncate'>" . htmlspecialchars($task_title) . "</div>";
                            }
                        }
                        echo "</div></div>";
                    }

                    // Print remaining empty cells
                    $remaining_cells = 7 - (($day_of_week + $days_in_month) % 7);
                    if ($remaining_cells < 7) {
                        for ($i = 0; $i < $remaining_cells; $i++) {
                            echo "<div class='border border-gray-800 rounded-lg p-2 h-32'></div>";
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
