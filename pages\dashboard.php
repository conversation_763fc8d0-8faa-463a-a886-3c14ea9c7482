<?php
require_once '../includes/session.php';
require_login();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Time Management System</title>
    <link href="../src/output.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white font-sans">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-800/50 backdrop-blur-lg border-r border-gray-700 p-6 flex-shrink-0">
            <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 mb-12">TaskFlow</h1>
            <nav class="space-y-2">
                <a href="dashboard.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 bg-purple-600/30 text-white border border-purple-500">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                    Dashboard
                </a>
                <a href="add_task.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    Add Task
                </a>
                <a href="calendar.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                    Calendar
                </a>
            </nav>
            <div class="absolute bottom-6 left-6">
                <a href="../logout.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50 text-gray-400 hover:text-white">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 sm:p-10">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h2 class="text-4xl font-bold">Dashboard</h2>
                    <p class="text-gray-400 mt-1">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</p>
                </div>
                <a href="add_task.php" class="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-2 px-4 rounded-lg hover:from-purple-600 hover:to-pink-600 transition transform hover:scale-105 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>
                    New Task
                </a>
            </div>
            
            <!-- Task Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                <?php
                $user_id = $_SESSION['user_id'];
                $sql = "SELECT id, title, description, priority, deadline, deadline_time, status FROM tasks WHERE user_id = ? ORDER BY created_at DESC";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $user_id);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    while ($task = $result->fetch_assoc()) {
                        // Determine status color
                        $status_color = 'bg-yellow-500'; // Pending
                        if ($task['status'] == 'In Progress') {
                            $status_color = 'bg-blue-500';
                        } elseif ($task['status'] == 'Completed') {
                            $status_color = 'bg-green-500';
                        }

                        // Determine priority color
                        $priority_color = 'border-yellow-500'; // Medium
                        if ($task['priority'] == 'Low') {
                            $priority_color = 'border-green-500';
                        } elseif ($task['priority'] == 'High') {
                            $priority_color = 'border-red-500';
                        }
                        
                        // Deadline formatting
                        $deadline_text = 'No Deadline';
                        $deadline_color = 'text-gray-400';
                        if ($task['deadline']) {
                            $deadline_date = new DateTime($task['deadline']);
                            $now = new DateTime();
                            $deadline_text = $deadline_date->format('M j, Y');
                            if ($task['status'] != 'Completed' && $deadline_date < $now) {
                                $deadline_color = 'text-red-400';
                                $deadline_text .= ' (Overdue)';
                            }
                        }
                ?>
                        <div class="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl shadow-lg p-6 flex flex-col justify-between transform hover:-translate-y-1 transition-transform duration-300">
                            <div class="flex justify-between items-start">
                                <h3 class="font-bold text-xl mb-2 text-white"><?php echo htmlspecialchars($task['title']); ?></h3>
                                <span class="px-3 py-1 text-xs font-bold text-white <?php echo $status_color; ?> rounded-full"><?php echo $task['status']; ?></span>
                            </div>
                            <p class="text-gray-400 text-sm mb-4 flex-grow"><?php echo htmlspecialchars($task['description']); ?></p>
                            <div class="mt-auto">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center text-sm <?php echo $deadline_color; ?>">
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                                        <?php echo $deadline_text; ?>
                                    </div>
                                    <div class="w-16 h-2 bg-gray-700 rounded-full">
                                        <div class="h-2 rounded-full <?php echo $priority_color; ?>" style="width: <?php echo ($task['priority'] == 'High' ? '100%' : ($task['priority'] == 'Medium' ? '66%' : '33%')); ?>"></div>
                                    </div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 border-t border-gray-700 pt-4">
                                    <a href="edit_task.php?id=<?php echo $task['id']; ?>" class="text-sm text-gray-400 hover:text-purple-400 transition flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z"></path></svg>
                                        Edit
                                    </a>
                                    <a href="../process/delete_task.php?id=<?php echo $task['id']; ?>" onclick="return confirm('Are you sure?')" class="text-sm text-gray-400 hover:text-red-400 transition flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                                        Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                <?php
                    }
                } else {
                    echo "<p class='text-gray-400'>No tasks found. <a href='add_task.php' class='text-purple-400 hover:underline'>Add a new task</a> to get started!</p>";
                }
                $stmt->close();
                ?>
            </div>
        </div>
    </div>
</body>
</html>
