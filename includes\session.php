<?php
// Start the session if it's not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include the database connection
require_once 'db_connect.php';

/**
 * Checks if a user is logged in.
 *
 * @return bool True if the user is logged in, false otherwise.
 */
function is_logged_in()
{
    return isset($_SESSION['user_id']);
}

/**
 * Redirects to the login page if the user is not logged in.
 */
function require_login()
{
    if (!is_logged_in()) {
        // Adjust the path based on the script's location
        $path_to_login = 'login.php';
        if (strpos($_SERVER['PHP_SELF'], '/pages/') !== false) {
            // Already in pages directory
        } else if (strpos($_SERVER['PHP_SELF'], '/process/') !== false) {
            $path_to_login = '../pages/login.php';
        } else {
            $path_to_login = 'pages/login.php';
        }
        header("Location: " . $path_to_login);
        exit();
    }
}
?>
