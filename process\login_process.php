<?php
require_once '../includes/session.php';
require_once '../includes/functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = sanitize_input($_POST['email']);
    $password = sanitize_input($_POST['password']);

    // Validate input
    if (empty($email) || empty($password)) {
        die("Please fill all required fields.");
    }

    // Retrieve user from the database
    $stmt = $conn->prepare("SELECT id, full_name, password FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        $stmt->bind_result($user_id, $full_name, $hashed_password);
        $stmt->fetch();

        // Verify password
        if (password_verify($password, $hashed_password)) {
            // Password is correct, start a new session
            $_SESSION['user_id'] = $user_id;
            $_SESSION['full_name'] = $full_name;

            header("Location: ../pages/dashboard.php");
            exit();
        } else {
            die("Invalid password.");
        }
    } else {
        die("No user found with that email address.");
    }

    $stmt->close();
    $conn->close();
}
?>
