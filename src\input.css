@import "tailwindcss";

/* Torch of Balance Animations */
.torch-flame {
    animation: flicker 2s ease-in-out infinite alternate;
}

@keyframes flicker {
    0% {
        transform: scale(1) rotate(-1deg);
        filter: brightness(1);
    }
    25% {
        transform: scale(1.05) rotate(1deg);
        filter: brightness(1.1);
    }
    50% {
        transform: scale(0.98) rotate(-0.5deg);
        filter: brightness(0.95);
    }
    75% {
        transform: scale(1.02) rotate(0.5deg);
        filter: brightness(1.05);
    }
    100% {
        transform: scale(1) rotate(0deg);
        filter: brightness(1);
    }
}

/* Floating particles animation */
@keyframes float-up {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-20px) scale(0.5);
        opacity: 0;
    }
}

.animate-float {
    animation: float-up 3s ease-out infinite;
}

