<?php
require_once '../includes/session.php';
require_login();
require_once '../includes/functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $task_id = sanitize_input($_POST['task_id']);
    $user_id = $_SESSION['user_id'];
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $priority = sanitize_input($_POST['priority']);
    $deadline = !empty($_POST['deadline']) ? sanitize_input($_POST['deadline']) : null;
    $deadline_time = !empty($_POST['deadline_time']) ? sanitize_input($_POST['deadline_time']) : null;
    $status = sanitize_input($_POST['status']);

    // Validate input
    if (empty($title)) {
        die("Title is a required field.");
    }

    // Update task in the database
    $stmt = $conn->prepare("UPDATE tasks SET title = ?, description = ?, priority = ?, deadline = ?, deadline_time = ?, status = ? WHERE id = ? AND user_id = ?");
    $stmt->bind_param("ssssssii", $title, $description, $priority, $deadline, $deadline_time, $status, $task_id, $user_id);

    if ($stmt->execute()) {
        header("Location: ../pages/dashboard.php?task=updated");
        exit();
    } else {
        die("Error: " . $stmt->error);
    }

    $stmt->close();
    $conn->close();
}
?>
