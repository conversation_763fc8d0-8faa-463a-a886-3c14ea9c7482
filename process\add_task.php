<?php
require_once '../includes/session.php';
require_login();
require_once '../includes/functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $user_id = $_SESSION['user_id'];
    $title = sanitize_input($_POST['title']);
    $description = sanitize_input($_POST['description']);
    $priority = sanitize_input($_POST['priority']);
    $deadline = !empty($_POST['deadline']) ? sanitize_input($_POST['deadline']) : null;
    $deadline_time = !empty($_POST['deadline_time']) ? sanitize_input($_POST['deadline_time']) : null;

    // Validate input
    if (empty($title)) {
        die("Title is a required field.");
    }

    // Insert task into the database
    $stmt = $conn->prepare("INSERT INTO tasks (user_id, title, description, priority, deadline, deadline_time) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("isssss", $user_id, $title, $description, $priority, $deadline, $deadline_time);

    if ($stmt->execute()) {
        header("Location: ../pages/dashboard.php?task=added");
        exit();
    } else {
        die("Error: " . $stmt->error);
    }

    $stmt->close();
    $conn->close();
}
?>
