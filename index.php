<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to TaskFlow - A New Era of Productivity</title>
    <link href="src/output.css" rel="stylesheet">
    <style>
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white font-sans">
    <div class="relative min-h-screen overflow-hidden">
        <!-- Background Glows -->
        <div class="absolute top-0 -left-4 w-72 h-72 bg-purple-600 rounded-full mix-blend-screen filter blur-xl opacity-30 animate-pulse"></div>
        <div class="absolute top-0 -right-4 w-72 h-72 bg-pink-600 rounded-full mix-blend-screen filter blur-xl opacity-30 animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-0 -left-4 w-72 h-72 bg-blue-600 rounded-full mix-blend-screen filter blur-xl opacity-30 animate-pulse animation-delay-4000"></div>

        <!-- Header -->
        <header class="sticky top-0 z-50 p-4 sm:p-6 bg-gray-900/50 backdrop-blur-lg border-b border-gray-800">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600">TaskFlow</h1>
                <nav class="hidden sm:flex items-center space-x-4">
                    <a href="pages/login.php" class="text-gray-300 hover:text-white transition">Login</a>
                    <a href="pages/register.php" class="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-2 px-5 rounded-lg hover:from-purple-600 hover:to-pink-600 transition transform hover:scale-105">Get Started</a>
                </nav>
                <button class="sm:hidden text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
        </header>

        <!-- Hero Section -->
        <main class="relative z-10 flex-grow flex items-center justify-center text-center py-20 sm:py-32">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl sm:text-5xl md:text-7xl font-extrabold mb-6 leading-tight animate-fade-in-down">
                    Master Your Day, <br class="hidden md:block" /> <span class="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600">Conquer Your Goals</span>
                </h2>
                <p class="max-w-3xl mx-auto text-lg md:text-xl text-gray-400 mb-10 animate-fade-in-up">
                    The ultimate tool for students, professionals, and teams to manage time, track progress, and achieve more with less stress.
                </p>
                <div class="flex justify-center space-x-4 animate-fade-in-up animation-delay-400">
                    <a href="pages/register.php" class="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-4 px-8 rounded-full text-lg hover:from-purple-600 hover:to-pink-600 transition transform hover:scale-105 shadow-lg shadow-purple-500/20">
                        Start for Free
                    </a>
                    <a href="#features" class="bg-gray-800 text-white font-bold py-4 px-8 rounded-full text-lg hover:bg-gray-700 transition">
                        Learn More
                    </a>
                </div>
            </div>
        </main>
    </div>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-900/70 backdrop-blur-lg">
        <div class="container mx-auto px-4 text-center">
            <h3 class="text-4xl font-bold mb-4">Why You'll Love TaskFlow</h3>
            <p class="text-gray-400 mb-12 max-w-2xl mx-auto">Everything you need to stay organized and motivated, in one beautiful package.</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-700 transform hover:-translate-y-2 transition duration-300">
                    <div class="floating inline-block bg-purple-500/20 p-4 rounded-full mb-6">
                        <svg class="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path></svg>
                    </div>
                    <h4 class="text-2xl font-bold mb-2">Intuitive Task Management</h4>
                    <p class="text-gray-400">Easily create, organize, and prioritize your tasks with a clean, drag-and-drop interface.</p>
                </div>
                <!-- Feature 2 -->
                <div class="bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-700 transform hover:-translate-y-2 transition duration-300">
                    <div class="floating inline-block bg-pink-500/20 p-4 rounded-full mb-6" style="animation-delay: 0.2s;">
                        <svg class="w-8 h-8 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                    </div>
                    <h4 class="text-2xl font-bold mb-2">Smart Calendar View</h4>
                    <p class="text-gray-400">Visualize your schedule, spot deadlines, and plan your weeks with our interactive calendar.</p>
                </div>
                <!-- Feature 3 -->
                <div class="bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-700 transform hover:-translate-y-2 transition duration-300">
                    <div class="floating inline-block bg-blue-500/20 p-4 rounded-full mb-6" style="animation-delay: 0.4s;">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path></svg>
                    </div>
                    <h4 class="text-2xl font-bold mb-2">Visual Reminders</h4>
                    <p class="text-gray-400">Never miss a deadline with color-coded priorities and status badges that keep you on track.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="p-6 text-center text-gray-500 border-t border-gray-800">
        &copy; <?php echo date('Y'); ?> TaskFlow. All rights reserved.
    </footer>
</body>
</html>
