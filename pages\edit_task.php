<?php
require_once '../includes/session.php';
require_login();
require_once '../includes/functions.php';

// Check if task ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: dashboard.php");
    exit();
}

$task_id = $_GET['id'];
$user_id = $_SESSION['user_id'];

// Fetch task details
$stmt = $conn->prepare("SELECT title, description, priority, deadline, deadline_time, status FROM tasks WHERE id = ? AND user_id = ?");
$stmt->bind_param("ii", $task_id, $user_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 1) {
    $task = $result->fetch_assoc();
} else {
    // Task not found or doesn't belong to the user
    header("Location: dashboard.php");
    exit();
}
$stmt->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Task - Time Management System</title>
    <link href="../src/output.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white font-sans">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="w-64 bg-gray-800/50 backdrop-blur-lg border-r border-gray-700 p-6 flex-shrink-0">
            <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600 mb-12">TaskFlow</h1>
            <nav class="space-y-2">
                <a href="dashboard.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
                    Dashboard
                </a>
                <a href="add_task.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                    Add Task
                </a>
                <a href="calendar.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                    Calendar
                </a>
            </nav>
            <div class="absolute bottom-6 left-6">
                <a href="../logout.php" class="flex items-center py-2.5 px-4 rounded-lg transition duration-200 hover:bg-gray-700/50 text-gray-400 hover:text-white">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path></svg>
                    Logout
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 sm:p-10">
            <h2 class="text-4xl font-bold mb-8">Edit Task</h2>
            <div class="w-full max-w-2xl bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl shadow-lg p-8">
                <form action="../process/update_task.php" method="POST" class="space-y-6">
                    <input type="hidden" name="task_id" value="<?php echo $task_id; ?>">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                        <input type="text" name="title" id="title" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition" value="<?php echo htmlspecialchars($task['title']); ?>" required>
                    </div>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                        <textarea name="description" id="description" rows="4" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition"><?php echo htmlspecialchars($task['description']); ?></textarea>
                    </div>
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-300 mb-2">Priority</label>
                        <select name="priority" id="priority" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition">
                            <option value="Low" <?php if ($task['priority'] == 'Low') echo 'selected'; ?>>Low</option>
                            <option value="Medium" <?php if ($task['priority'] == 'Medium') echo 'selected'; ?>>Medium</option>
                            <option value="High" <?php if ($task['priority'] == 'High') echo 'selected'; ?>>High</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="deadline" class="block text-sm font-medium text-gray-300 mb-2">Deadline</label>
                            <input type="date" name="deadline" id="deadline" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition" value="<?php echo $task['deadline']; ?>">
                        </div>
                        <div>
                            <label for="deadline_time" class="block text-sm font-medium text-gray-300 mb-2">Time</label>
                            <input type="time" name="deadline_time" id="deadline_time" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition" value="<?php echo $task['deadline_time']; ?>">
                        </div>
                    </div>
                     <div>
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-2">Status</label>
                        <select name="status" id="status" class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition">
                            <option value="Pending" <?php if ($task['status'] == 'Pending') echo 'selected'; ?>>Pending</option>
                            <option value="In Progress" <?php if ($task['status'] == 'In Progress') echo 'selected'; ?>>In Progress</option>
                            <option value="Completed" <?php if ($task['status'] == 'Completed') echo 'selected'; ?>>Completed</option>
                        </select>
                    </div>
                    <button type="submit" class="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold py-3 px-4 rounded-lg hover:from-purple-600 hover:to-pink-600 transition transform hover:scale-105">Update Task</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
