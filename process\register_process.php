<?php
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $full_name = sanitize_input($_POST['full_name']);
    $email = sanitize_input($_POST['email']);
    $password = sanitize_input($_POST['password']);

    // Validate input
    if (empty($full_name) || empty($email) || empty($password)) {
        header("Location: ../pages/register.php?error=" . urlencode("Please fill all required fields."));
        exit();
    }

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        header("Location: ../pages/register.php?error=" . urlencode("Invalid email format."));
        exit();
    }

    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows > 0) {
        header("Location: ../pages/register.php?error=" . urlencode("Email already registered."));
        exit();
    }

    $stmt->close();

    // Hash the password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Insert user into the database
    $stmt = $conn->prepare("INSERT INTO users (full_name, email, password) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $full_name, $email, $hashed_password);

    if ($stmt->execute()) {
        header("Location: ../pages/login.php?registration=success");
        exit();
    } else {
        header("Location: ../pages/register.php?error=" . urlencode("Registration failed. Please try again."));
        exit();
    }

    $stmt->close();
    $conn->close();
}
?>
